{"name": "diamond-aircraft/diamond-pilotshop", "description": "www.diamond-pilotshop.com shop", "type": "project", "license": ["proprietary"], "require": {"aheadworks/module-customer-group-catalog-permissions": "1.0.12", "avstudnitz/scopehint2": "~1.1.2", "copex/theme-admin-green": "^1.0", "copex/theme-hyva-green": "^1.1", "cweagans/composer-patches": "^1.7.3", "experius/module-wysiwygdownloads": "1.1.0", "firebear/importexport": "^3.8", "firegento/magesetup2": "^1.2", "gene/module-encryption-key-manager": "0.0.14-alpha", "magento/composer-root-update-plugin": "^2.0.3", "magento/product-community-edition": "2.4.6-p11", "magepal/magento2-gmailsmtpapp": "^2.9.1", "mollie/magento2": "^2.34", "n98/magerun2-dist": "^6.1.1", "splendidinternet/mage2-locale-de-de": "^1.70", "symfony/yaml": "^5.3.14", "yireo/magento2-googletagmanager2": "2.1.3"}, "require-dev": {"allure-framework/allure-phpunit": "^2.1", "deployer/deployer": "^7", "mage2tv/magento-cache-clean": "^1.1", "msp/devtools": "^1.3", "phpcompatibility/php-compatibility": "^9.3.5", "phpstan/phpstan": "^1.10.19", "phpunit/phpunit": "^9.6.9", "sebastian/phpcpd": "^6.0.3", "tddwizard/magento2-fixtures": "^1.1.2"}, "replace": {"amzn/amazon-pay-and-login-magento-2-module": "*", "amzn/amazon-pay-and-login-with-amazon-core-module": "*", "amzn/amazon-pay-module": "*", "amzn/amazon-pay-sdk-php": "*", "amzn/login-with-amazon-module": "*", "astock/stock-api-libphp": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-b2b": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "dotmailer/dotmailer-magento2-extension-chat-graph-ql": "*", "dotmailer/dotmailer-magento2-extension-graph-ql": "*", "dotmailer/dotmailer-magento2-extension-sms": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-enterprise-package": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "klarna/m2-payments": "*", "klarna/module-core": "*", "klarna/module-kp": "*", "klarna/module-kp-graph-ql": "*", "klarna/module-ordermanagement": "*", "klarna/module-onsitemessaging": "*", "vertex/module-address-validation": "*", "magento/module-amqp": "*", "magento/module-amqp-store": "*", "magento/google-shopping-ads": "*", "magento/adobe-stock-integration": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-two-factor-auth": "*", "paypal/module-braintree": "*", "temando/module-shipping": "*", "temando/module-shipping-m2": "*", "temando/module-shipping-remover": "*", "vertex/module-tax": "*", "vertex/module-tax-staging": "*", "vertex/product-magento-module": "*", "vertex/sdk": "*", "vertexinc/module-tax-staging": "*", "vertexinc/product-magento-module": "*", "vertexinc/product-magento-module-commerce": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*"}, "conflict": {"gene/bluefoot": "*"}, "autoload": {"psr-4": {"Magento\\Setup\\": "setup/src/Magento/Setup/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}, "psr-0": {"": ["app/code/", "generated/code/"]}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"]}, "autoload-dev": {}, "minimum-stability": "stable", "config": {"use-include-path": true, "preferred-install": "dist", "sort-packages": true, "platform": {"php": "8.1"}, "allow-plugins": {"magento/composer-dependency-version-audit-plugin": true, "magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "laminas/laminas-dependency-plugin": true, "cweagans/composer-patches": true, "magento/composer-root-update-plugin": true}}, "repositories": {"firebear": {"type": "composer", "url": "https://firebearstudio.com/composer/download/package/type/ce/user/3a1b49b6e56a4adea66d44b65d724197/password/24d34bbf224db00aa5d4c6c174cfa0da/"}, "aheadworks": {"type": "composer", "url": "https://dist.aheadworks.com/"}, "magento": {"type": "composer", "url": "https://repo.magento.com"}, "hyva": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/metall-im-garten-com/"}, "copex": {"type": "composer", "url": "https://diamondaircraft:<EMAIL>"}, "copex/mspdev": {"type": "vcs", "url": "https://github.com/CopeX/m2-MSP_DevTools"}}, "extra": {"magento-force": "override", "magento-root-dir": "./", "composer-exit-on-patch-failure": true, "patches": {"zendframework/zend-mail": {"DIAMONDREL-130": "./patches/email-attached-issue.patch"}, "staempfli/magento2-module-image-resizer": {"PETCOWART-704 Call to undefined method Magento\\Framework\\Logger\\LoggerProxy::addError()": "./patches/staempfli_image_resizer.patch"}, "magento/module-page-builder": {"PETCOWART-1218 PageBuilder video iframe lazy loading": "./patches/0001-PETCOWART-1218-PageBuilder-video-iframe-lazy-loading.patch", "PETCOWART-1326 PageBuilder img tag lazy loading": "./patches/0001-PETCOWART-1326-add-lazy-loading-to-pagebuilder-image.patch", "DIAMOND01-92 Tiny MCE font size selector": "./patches/fix_security_patch-24-6-p8-page_builder_tinymce_font_size_selector.patch"}}}, "scripts": {}}