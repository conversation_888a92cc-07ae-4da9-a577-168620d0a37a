<?xml version="1.0"?>
<!--
/**
 * Diamond Aircraft Homepage Layout
 * Layout configuration for the homepage
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    
    <head>
        <!-- Homepage specific meta tags -->
        <meta name="description" content="Diamond Pilot Shop - Official Diamond Aircraft Online Shop. Premium aviation apparel, accessories, and pilot gear. #WeWearDiamondAircraft"/>
        <meta name="keywords" content="Diamond Aircraft, pilot shop, aviation apparel, pilot gear, aircraft merchandise"/>
        <meta name="robots" content="index,follow"/>
        
        <!-- Open Graph meta tags -->
        <meta property="og:type" content="website"/>
        <meta property="og:title" content="Diamond Pilot Shop - Official Diamond Aircraft Online Shop"/>
        <meta property="og:description" content="Discover our premium collection of Diamond Aircraft apparel and accessories. #WeWearDiamondAircraft"/>
        <meta property="og:image" content="{{media url='homepage/og-image.jpg'}}"/>
        
        <!-- Twitter Card meta tags -->
        <meta name="twitter:card" content="summary_large_image"/>
        <meta name="twitter:title" content="Diamond Pilot Shop - Official Diamond Aircraft Online Shop"/>
        <meta name="twitter:description" content="Discover our premium collection of Diamond Aircraft apparel and accessories. #WeWearDiamondAircraft"/>
        <meta name="twitter:image" content="{{media url='homepage/twitter-image.jpg'}}"/>
        
        <!-- Structured Data for Homepage -->
        <script type="application/ld+json">
        {
            "@context": "https://schema.org",
            "@type": "Organization",
            "name": "Diamond Pilot Shop",
            "url": "{{store url=''}}",
            "logo": "{{media url='logo/diamond-logo.svg'}}",
            "description": "Official Diamond Aircraft Online Shop offering premium aviation apparel, accessories, and pilot gear.",
            "address": {
                "@type": "PostalAddress",
                "streetAddress": "Ferdinand-Graf-von-Zeppelin-Straße 1",
                "addressLocality": "Wiener Neustadt",
                "postalCode": "2700",
                "addressCountry": "AT"
            },
            "contactPoint": {
                "@type": "ContactPoint",
                "telephone": "+43-2622-26700-1100",
                "contactType": "customer service",
                "email": "<EMAIL>"
            },
            "sameAs": [
                "https://www.facebook.com/DiamondAircraft",
                "https://www.instagram.com/diamondaircraft",
                "https://www.youtube.com/user/DiamondAircraft"
            ]
        }
        </script>
        
        <!-- Homepage specific CSS -->
        <css src="css/diamond-homepage.css"/>
        
        <!-- Preload critical resources -->
        <link rel="preload" href="{{media url='images/hero/header_home_da50rg_5_2.jpg'}}" as="image"/>
    </head>
    
    <body>
        <!-- Add homepage-specific body class -->
        <attribute name="class" value="diamond-homepage"/>
        
        <!-- Remove default page title for homepage -->
        <referenceBlock name="page.main.title" remove="true"/>
        
        <!-- Update main content area -->
        <referenceContainer name="main">
            <!-- Remove default CMS content block -->
            <referenceBlock name="cms_page" remove="true"/>
            
            <!-- Add our custom homepage content -->
            <block class="Magento\Framework\View\Element\Template" 
                   name="diamond.homepage" 
                   template="Magento_Cms::homepage.phtml">
                <arguments>
                    <argument name="cache_lifetime" xsi:type="number">86400</argument>
                    <argument name="cache_tags" xsi:type="array">
                        <item name="diamond_homepage" xsi:type="string">diamond_homepage</item>
                    </argument>
                </arguments>
            </block>
        </referenceContainer>
        
        <!-- Add homepage-specific JavaScript -->
        <referenceContainer name="before.body.end">
            <block class="Magento\Framework\View\Element\Template" 
                   name="diamond.homepage.scripts" 
                   template="Magento_Cms::homepage-scripts.phtml"/>
        </referenceContainer>
    </body>
</page>
