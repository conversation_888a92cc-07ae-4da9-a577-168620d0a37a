<?php
/**
 * Diamond Aircraft Product Slider Template
 * This template creates a horizontal product carousel matching the original design
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

// Get products from block data
$products = $block->getData('products') ?? [];
$title = $block->getData('title') ?? __('Products');
$sliderId = $block->getData('slider_id') ?? 'product-slider-' . uniqid();
?>

<?php if (!empty($products)): ?>
<!-- Diamond Aircraft Product Slider -->
<div class="product-list py-12 bg-white" x-data="productSlider<?= $escaper->escapeHtml($sliderId) ?>()">
    <!-- Section Header -->
    <div class="max-w-screen-2xl mx-auto px-4 mb-8">
        <div class="text-center">
            <h2 class="text-2xl md:text-3xl font-bold text-gray-800 mb-4">
                <?= $escaper->escapeHtml($title) ?>
            </h2>
        </div>
    </div>

    <!-- Slider Container -->
    <div class="max-w-screen-2xl mx-auto px-4">
        <div class="relative">
            <!-- Slider Controls -->
            <div class="flex justify-between items-center mb-6">
                <div class="flex space-x-2">
                    <button 
                        @click="prevSlide()"
                        class="bg-white border border-gray-300 rounded-full p-3 hover:bg-gray-50 hover:border-diamond-blue transition-all duration-200 shadow-sm"
                        :disabled="currentSlide === 0"
                        :class="{ 'opacity-50 cursor-not-allowed': currentSlide === 0 }"
                    >
                        <?= $heroicons->chevronLeftHtml('w-5 h-5 text-gray-600', 20, 20) ?>
                    </button>
                    <button 
                        @click="nextSlide()"
                        class="bg-white border border-gray-300 rounded-full p-3 hover:bg-gray-50 hover:border-diamond-blue transition-all duration-200 shadow-sm"
                        :disabled="currentSlide >= maxSlide"
                        :class="{ 'opacity-50 cursor-not-allowed': currentSlide >= maxSlide }"
                    >
                        <?= $heroicons->chevronRightHtml('w-5 h-5 text-gray-600', 20, 20) ?>
                    </button>
                </div>
            </div>

            <!-- Products Slider -->
            <div class="overflow-hidden">
                <div 
                    class="flex transition-transform duration-300 ease-in-out"
                    :style="`transform: translateX(-${currentSlide * slideWidth}%)`"
                >
                    <?php foreach ($products as $product): ?>
                        <div class="flex-none w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 px-3">
                            <!-- Product Card -->
                            <div class="group bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-200 hover:border-diamond-blue h-full flex flex-col">
                                
                                <!-- Product Image -->
                                <div class="relative overflow-hidden aspect-square">
                                    <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>" 
                                       class="block w-full h-full">
                                        <?php $productImage = $block->getImage($product, 'category_page_grid'); ?>
                                        <img 
                                            src="<?= $escaper->escapeUrl($productImage->getImageUrl()) ?>"
                                            alt="<?= $escaper->escapeHtmlAttr($product->getName()) ?>"
                                            class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                            loading="lazy"
                                        />
                                    </a>
                                    
                                    <!-- Sale Badge -->
                                    <?php if ($product->getSpecialPrice()): ?>
                                        <div class="absolute top-3 left-3 bg-secondary text-white text-xs font-bold px-2 py-1 rounded-md">
                                            <?= $escaper->escapeHtml(__('SALE')) ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <!-- Quick Actions -->
                                    <div class="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                                        <button 
                                            type="button"
                                            class="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-sm hover:shadow-md transition-all duration-200"
                                            title="<?= $escaper->escapeHtmlAttr(__('Add to Wishlist')) ?>"
                                        >
                                            <?= $heroicons->heartHtml('w-4 h-4 text-gray-600 hover:text-red-500', 16, 16) ?>
                                        </button>
                                    </div>
                                </div>
                                
                                <!-- Product Details -->
                                <div class="flex flex-col flex-1 p-4">
                                    <!-- Product Name -->
                                    <h3 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2 hover:text-diamond-blue transition-colors duration-200">
                                        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>">
                                            <?= $escaper->escapeHtml($product->getName()) ?>
                                        </a>
                                    </h3>
                                    
                                    <!-- Price -->
                                    <div class="mb-3 mt-auto">
                                        <?php $priceRender = $block->getLayout()->getBlock('product.price.render.default'); ?>
                                        <?php if ($priceRender): ?>
                                            <div class="text-lg font-bold text-diamond-blue">
                                                <?= $priceRender->render('final_price', $product, [
                                                    'include_container' => true,
                                                    'display_minimal_price' => true,
                                                    'zone' => 'item_list',
                                                    'list_category_page' => true
                                                ]) ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Stock Status -->
                                    <div class="mb-3">
                                        <?php if ($product->isAvailable()): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                                                    <circle cx="4" cy="4" r="3"/>
                                                </svg>
                                                <?= $escaper->escapeHtml(__('In Stock')) ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                                <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                                                    <circle cx="4" cy="4" r="3"/>
                                                </svg>
                                                <?= $escaper->escapeHtml(__('Out of Stock')) ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <!-- Add to Cart Button -->
                                    <?php if ($product->isSaleable()): ?>
                                        <button 
                                            type="button"
                                            class="w-full bg-diamond-blue hover:bg-diamond-blue-dark text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 text-sm"
                                        >
                                            <?= $escaper->escapeHtml(__('Add to Cart')) ?>
                                        </button>
                                    <?php else: ?>
                                        <a 
                                            href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
                                            class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-2 px-4 rounded-md transition-colors duration-200 text-center inline-block text-sm"
                                        >
                                            <?= $escaper->escapeHtml(__('View Details')) ?>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function productSlider<?= $escaper->escapeHtml($sliderId) ?>() {
    return {
        currentSlide: 0,
        slideWidth: 20, // 20% for 5 items per view on desktop
        maxSlide: <?= max(0, count($products) - 5) ?>, // Show 5 items at once
        
        init() {
            this.updateSlideWidth();
            window.addEventListener('resize', () => this.updateSlideWidth());
        },
        
        updateSlideWidth() {
            const width = window.innerWidth;
            if (width < 640) {
                this.slideWidth = 100; // 1 item on mobile
                this.maxSlide = <?= max(0, count($products) - 1) ?>;
            } else if (width < 768) {
                this.slideWidth = 50; // 2 items on small tablets
                this.maxSlide = <?= max(0, count($products) - 2) ?>;
            } else if (width < 1024) {
                this.slideWidth = 33.333; // 3 items on tablets
                this.maxSlide = <?= max(0, count($products) - 3) ?>;
            } else if (width < 1280) {
                this.slideWidth = 25; // 4 items on small desktop
                this.maxSlide = <?= max(0, count($products) - 4) ?>;
            } else {
                this.slideWidth = 20; // 5 items on large desktop
                this.maxSlide = <?= max(0, count($products) - 5) ?>;
            }
        },
        
        nextSlide() {
            if (this.currentSlide < this.maxSlide) {
                this.currentSlide++;
            }
        },
        
        prevSlide() {
            if (this.currentSlide > 0) {
                this.currentSlide--;
            }
        }
    }
}
</script>
<?php endif; ?>
