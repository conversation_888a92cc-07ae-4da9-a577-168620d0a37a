<?php
/**
 * Diamond Aircraft Homepage Template
 * Main homepage layout combining all sections from the Live Site
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

// Sample product data - in real implementation, this would come from Magento
$topSellerProducts = [
    [
        'name' => 'Diamond Aircraft Polo Shirt',
        'image' => 'products/polo-shirt.jpg',
        'price' => '€49.90',
        'original_price' => '€59.90',
        'url' => '/diamond-aircraft-polo-shirt.html',
        'rating' => 5,
        'review_count' => 12,
        'stock_status' => 'In Stock',
        'badges' => ['Sale']
    ],
    [
        'name' => 'Diamond Aircraft Cap',
        'image' => 'products/cap.jpg',
        'price' => '€24.90',
        'url' => '/diamond-aircraft-cap.html',
        'rating' => 4,
        'review_count' => 8,
        'stock_status' => 'In Stock'
    ],
    [
        'name' => 'Diamond Aircraft Jacket',
        'image' => 'products/jacket.jpg',
        'price' => '€129.90',
        'url' => '/diamond-aircraft-jacket.html',
        'rating' => 5,
        'review_count' => 15,
        'stock_status' => 'In Stock'
    ],
    [
        'name' => 'Diamond Aircraft T-Shirt',
        'image' => 'products/t-shirt.jpg',
        'price' => '€29.90',
        'url' => '/diamond-aircraft-t-shirt.html',
        'rating' => 4,
        'review_count' => 22,
        'stock_status' => 'In Stock'
    ],
    [
        'name' => 'Diamond Aircraft Hoodie',
        'image' => 'products/hoodie.jpg',
        'price' => '€79.90',
        'url' => '/diamond-aircraft-hoodie.html',
        'rating' => 5,
        'review_count' => 18,
        'stock_status' => 'In Stock'
    ]
];

$newProducts = [
    [
        'name' => 'Diamond Aircraft Pilot Bag',
        'image' => 'products/pilot-bag.jpg',
        'price' => '€199.90',
        'url' => '/diamond-aircraft-pilot-bag.html',
        'rating' => 5,
        'review_count' => 5,
        'stock_status' => 'In Stock',
        'badges' => ['New']
    ],
    [
        'name' => 'Diamond Aircraft Sunglasses',
        'image' => 'products/sunglasses.jpg',
        'price' => '€89.90',
        'url' => '/diamond-aircraft-sunglasses.html',
        'rating' => 4,
        'review_count' => 3,
        'stock_status' => 'In Stock',
        'badges' => ['New']
    ]
];

$categories = [
    [
        'name' => 'Apparel',
        'image' => 'categories/apparel.jpg',
        'url' => '/apparel.html',
        'description' => 'Premium Diamond Aircraft clothing collection'
    ],
    [
        'name' => 'Accessories',
        'image' => 'categories/accessories.jpg',
        'url' => '/accessories.html',
        'description' => 'Professional pilot accessories and gear'
    ],
    [
        'name' => 'Gifts',
        'image' => 'categories/gifts.jpg',
        'url' => '/gifts.html',
        'description' => 'Perfect gifts for aviation enthusiasts'
    ]
];
?>

<!-- Diamond Aircraft Homepage -->
<div class="homepage">

    <!-- Hero Section -->
    <?= $block->getLayout()
        ->createBlock(Template::class)
        ->setTemplate('Magento_Cms::homepage-hero.phtml')
        ->toHtml() ?>

    <!-- Top Seller Products Section -->
    <?= $block->getLayout()
        ->createBlock(Template::class)
        ->setTemplate('Magento_Cms::homepage-products.phtml')
        ->setData([
            'products' => $topSellerProducts,
            'section_title' => __('Top Seller'),
            'section_id' => 'top-seller',
            'view_all_link' => '/catalog/bestsellers.html'
        ])
        ->toHtml() ?>

    <!-- Categories Showcase -->
    <section class="categories-showcase py-16 bg-white">
        <div class="max-w-screen-2xl mx-auto px-4">

            <!-- Section Header -->
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                    <?= $escaper->escapeHtml(__('Shop by Category')) ?>
                </h2>
                <div class="w-24 h-1 bg-diamond-blue mx-auto mb-6"></div>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    <?= $escaper->escapeHtml(__('Discover our complete range of Diamond Aircraft merchandise and professional aviation gear.')) ?>
                </p>
            </div>

            <!-- Categories Grid -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <?php foreach ($categories as $category): ?>
                <div class="group relative overflow-hidden rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2">

                    <!-- Category Image -->
                    <div class="aspect-[4/3] overflow-hidden">
                        <img
                            src="<?= $escaper->escapeUrl($block->getViewFileUrl('images/' . $category['image'])) ?>"
                            alt="<?= $escaper->escapeHtmlAttr($category['name']) ?>"
                            class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                            loading="lazy"
                        />
                    </div>

                    <!-- Category Overlay -->
                    <div class="absolute inset-0 bg-gradient-to-t from-black via-transparent to-transparent opacity-60 group-hover:opacity-80 transition-opacity duration-300"></div>

                    <!-- Category Content -->
                    <div class="absolute bottom-0 left-0 right-0 p-6 text-white">
                        <h3 class="text-2xl font-bold mb-2 group-hover:text-diamond-yellow transition-colors duration-300">
                            <?= $escaper->escapeHtml($category['name']) ?>
                        </h3>
                        <p class="text-gray-200 mb-4 opacity-90">
                            <?= $escaper->escapeHtml($category['description']) ?>
                        </p>
                        <a href="<?= $escaper->escapeUrl($category['url']) ?>"
                           class="inline-flex items-center text-white hover:text-diamond-yellow font-semibold transition-colors duration-200">
                            <?= $escaper->escapeHtml(__('Shop Now')) ?>
                            <span class="ml-2 transform group-hover:translate-x-1 transition-transform duration-200">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- New Products Section -->
    <?= $block->getLayout()
        ->createBlock(Template::class)
        ->setTemplate('Magento_Cms::homepage-products.phtml')
        ->setData([
            'products' => $newProducts,
            'section_title' => __('New Arrivals'),
            'section_id' => 'new-arrivals',
            'view_all_link' => '/catalog/new.html'
        ])
        ->toHtml() ?>

    <!-- Brand Story Section -->
    <section class="brand-story py-16 bg-gradient-to-br from-diamond-blue to-diamond-blue-dark text-white">
        <div class="max-w-screen-2xl mx-auto px-4">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">

                <!-- Content -->
                <div>
                    <h2 class="text-3xl md:text-4xl font-bold mb-6">
                        <?= $escaper->escapeHtml(__('Diamond Aircraft Heritage')) ?>
                    </h2>
                    <p class="text-lg mb-6 opacity-90 leading-relaxed">
                        <?= $escaper->escapeHtml(__('Since 1981, Diamond Aircraft has been at the forefront of aviation innovation. Our pilot shop brings you the same commitment to quality and excellence that defines our aircraft.')) ?>
                    </p>
                    <p class="text-lg mb-8 opacity-90 leading-relaxed">
                        <?= $escaper->escapeHtml(__('From professional pilot gear to premium apparel, every item in our collection reflects the precision and craftsmanship that Diamond Aircraft is known for worldwide.')) ?>
                    </p>
                    <div class="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                        <a href="/about-us.html"
                           class="inline-flex items-center justify-center px-8 py-4 bg-white text-diamond-blue font-bold rounded-lg hover:bg-gray-100 transition-colors duration-300">
                            <?= $escaper->escapeHtml(__('Our Story')) ?>
                        </a>
                        <a href="https://www.diamondaircraft.com"
                           target="_blank"
                           rel="noopener"
                           class="inline-flex items-center justify-center px-8 py-4 border-2 border-white text-white font-bold rounded-lg hover:bg-white hover:text-diamond-blue transition-all duration-300">
                            <?= $escaper->escapeHtml(__('Visit Diamond Aircraft')) ?>
                            <span class="ml-2">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"/>
                                </svg>
                            </span>
                        </a>
                    </div>
                </div>

                <!-- Image -->
                <div class="relative">
                    <div class="aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                        <img
                            src="<?= $escaper->escapeUrl($block->getViewFileUrl('images/diamond-aircraft-story.jpg')) ?>"
                            alt="<?= $escaper->escapeHtmlAttr(__('Diamond Aircraft Heritage')) ?>"
                            class="w-full h-full object-cover"
                            loading="lazy"
                        />
                    </div>
                    <!-- Decorative Elements -->
                    <div class="absolute -top-4 -right-4 w-24 h-24 bg-diamond-yellow rounded-full opacity-20"></div>
                    <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-white rounded-full opacity-20"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Signup -->
    <section class="newsletter py-16 bg-gray-50">
        <div class="max-w-screen-2xl mx-auto px-4 text-center">
            <div class="max-w-2xl mx-auto">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                    <?= $escaper->escapeHtml(__('Stay Updated')) ?>
                </h2>
                <p class="text-lg text-gray-600 mb-8">
                    <?= $escaper->escapeHtml(__('Subscribe to our newsletter and be the first to know about new products, exclusive offers, and Diamond Aircraft news.')) ?>
                </p>

                <!-- Newsletter Form -->
                <form class="flex flex-col sm:flex-row max-w-md mx-auto space-y-4 sm:space-y-0">
                    <input
                        type="email"
                        placeholder="<?= $escaper->escapeHtmlAttr(__('Enter your email address')) ?>"
                        class="flex-1 px-6 py-4 border border-gray-300 rounded-lg sm:rounded-r-none focus:outline-none focus:ring-2 focus:ring-diamond-blue focus:border-transparent"
                        required
                    />
                    <button
                        type="submit"
                        class="px-8 py-4 bg-diamond-blue hover:bg-diamond-blue-dark text-white font-bold rounded-lg sm:rounded-l-none transition-colors duration-300 transform hover:scale-105"
                    >
                        <?= $escaper->escapeHtml(__('Subscribe')) ?>
                    </button>
                </form>

                <p class="text-sm text-gray-500 mt-4">
                    <?= $escaper->escapeHtml(__('We respect your privacy. Unsubscribe at any time.')) ?>
                </p>
            </div>
        </div>
    </section>
</div>
