<?php
/**
 * Diamond Aircraft Homepage Product Showcase
 * Replicates the product carousel sections from the Live Site
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

// Get products from block data or use sample data
$products = $block->getData('products') ?: [];
$sectionTitle = $block->getData('section_title') ?: __('Top Seller');
$sectionId = $block->getData('section_id') ?: 'top-seller';
$showViewAll = $block->getData('show_view_all') !== false;
$viewAllLink = $block->getData('view_all_link') ?: '/catalog/';
?>

<script>
function productShowcase<?= $escaper->escapeHtml(ucfirst($sectionId)) ?>() {
    return {
        currentSlide: 0,
        itemsPerView: 5,
        totalItems: <?= count($products) ?>,

        init() {
            this.updateItemsPerView();
            window.addEventListener('resize', () => this.updateItemsPerView());
        },

        updateItemsPerView() {
            const width = window.innerWidth;
            if (width < 640) {
                this.itemsPerView = 1;
            } else if (width < 768) {
                this.itemsPerView = 2;
            } else if (width < 1024) {
                this.itemsPerView = 3;
            } else if (width < 1280) {
                this.itemsPerView = 4;
            } else {
                this.itemsPerView = 5;
            }
        },

        get maxSlide() {
            return Math.max(0, this.totalItems - this.itemsPerView);
        },

        get canSlidePrev() {
            return this.currentSlide > 0;
        },

        get canSlideNext() {
            return this.currentSlide < this.maxSlide;
        },

        slidePrev() {
            if (this.canSlidePrev) {
                this.currentSlide--;
            }
        },

        slideNext() {
            if (this.canSlideNext) {
                this.currentSlide++;
            }
        },

        get slideWidth() {
            return 100 / this.itemsPerView;
        }
    }
}
</script>

<?php if (!empty($products)): ?>
<!-- Diamond Aircraft Product Showcase -->
<section class="product-showcase py-16 bg-gray-50" x-data="productShowcase<?= $escaper->escapeHtml(ucfirst($sectionId)) ?>()" x-init="init()">
    <div class="max-w-screen-2xl mx-auto px-4">

        <!-- Section Header -->
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                <?= $escaper->escapeHtml($sectionTitle) ?>
            </h2>
            <div class="w-24 h-1 bg-diamond-blue mx-auto mb-6"></div>
            <?php if ($showViewAll): ?>
            <a href="<?= $escaper->escapeUrl($viewAllLink) ?>"
               class="inline-flex items-center text-diamond-blue hover:text-diamond-blue-dark font-semibold transition-colors duration-200">
                <?= $escaper->escapeHtml(__('View All Products')) ?>
                <span class="ml-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </span>
            </a>
            <?php endif; ?>
        </div>

        <!-- Products Slider Container -->
        <div class="relative">

            <!-- Navigation Buttons -->
            <div class="absolute -top-16 right-0 flex space-x-2 z-10">
                <button
                    @click="slidePrev()"
                    :disabled="!canSlidePrev"
                    :class="canSlidePrev ? 'bg-white hover:bg-gray-50 text-gray-700 hover:text-diamond-blue' : 'bg-gray-100 text-gray-400 cursor-not-allowed'"
                    class="p-3 rounded-full shadow-md transition-all duration-200 border border-gray-200"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Previous products')) ?>"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                    </svg>
                </button>

                <button
                    @click="slideNext()"
                    :disabled="!canSlideNext"
                    :class="canSlideNext ? 'bg-white hover:bg-gray-50 text-gray-700 hover:text-diamond-blue' : 'bg-gray-100 text-gray-400 cursor-not-allowed'"
                    class="p-3 rounded-full shadow-md transition-all duration-200 border border-gray-200"
                    aria-label="<?= $escaper->escapeHtmlAttr(__('Next products')) ?>"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                    </svg>
                </button>
            </div>

            <!-- Products Slider -->
            <div class="overflow-hidden">
                <div
                    class="flex transition-transform duration-500 ease-in-out"
                    :style="`transform: translateX(-${currentSlide * slideWidth}%)`"
                >
                    <?php foreach ($products as $product): ?>
                    <div class="flex-none w-full sm:w-1/2 md:w-1/3 lg:w-1/4 xl:w-1/5 px-3">
                        <!-- Product Card -->
                        <div class="group bg-white rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-diamond-blue h-full flex flex-col">

                            <!-- Product Image -->
                            <div class="relative aspect-square overflow-hidden bg-gray-50">
                                <img
                                    src="<?= $escaper->escapeUrl($product['image'] ?? '') ?>"
                                    alt="<?= $escaper->escapeHtmlAttr($product['name'] ?? '') ?>"
                                    class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                                    loading="lazy"
                                />

                                <!-- Product Badges -->
                                <?php if (!empty($product['badges'])): ?>
                                <div class="absolute top-3 left-3 flex flex-col space-y-2">
                                    <?php foreach ($product['badges'] as $badge): ?>
                                    <span class="px-2 py-1 text-xs font-semibold text-white bg-diamond-blue rounded-full">
                                        <?= $escaper->escapeHtml($badge) ?>
                                    </span>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>

                                <!-- Quick Actions -->
                                <div class="absolute top-3 right-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                                    <div class="flex flex-col space-y-2">
                                        <button class="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200">
                                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                                            </svg>
                                        </button>
                                        <button class="p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors duration-200">
                                            <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- Product Info -->
                            <div class="p-4 flex-1 flex flex-col">

                                <!-- Product Name -->
                                <h3 class="font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-diamond-blue transition-colors duration-200">
                                    <a href="<?= $escaper->escapeUrl($product['url'] ?? '#') ?>" class="hover:no-underline">
                                        <?= $escaper->escapeHtml($product['name'] ?? '') ?>
                                    </a>
                                </h3>

                                <!-- Product Rating -->
                                <?php if (!empty($product['rating'])): ?>
                                <div class="flex items-center mb-2">
                                    <div class="flex text-yellow-400">
                                        <?php for ($i = 1; $i <= 5; $i++): ?>
                                            <?php if ($i <= $product['rating']): ?>
                                                <svg class="w-4 h-4 fill-current" viewBox="0 0 24 24">
                                                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                                                </svg>
                                            <?php else: ?>
                                                <svg class="w-4 h-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                                </svg>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                    <span class="text-sm text-gray-500 ml-2">(<?= $escaper->escapeHtml($product['review_count'] ?? '0') ?>)</span>
                                </div>
                                <?php endif; ?>

                                <!-- Product Price -->
                                <div class="mt-auto">
                                    <div class="flex items-center justify-between">
                                        <div class="text-lg font-bold text-diamond-blue">
                                            <?= $escaper->escapeHtml($product['price'] ?? '') ?>
                                        </div>
                                        <?php if (!empty($product['original_price']) && $product['original_price'] !== $product['price']): ?>
                                        <div class="text-sm text-gray-500 line-through">
                                            <?= $escaper->escapeHtml($product['original_price']) ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Stock Status -->
                                    <div class="text-sm text-green-600 mt-1">
                                        <?= $escaper->escapeHtml($product['stock_status'] ?? __('In Stock')) ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section>
<?php endif; ?>
