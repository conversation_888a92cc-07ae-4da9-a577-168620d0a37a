<?php
/**
 * Diamond Aircraft Homepage Scripts
 * Additional JavaScript functionality for the homepage
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.product-showcase, .categories-showcase, .brand-story, .newsletter').forEach(el => {
        observer.observe(el);
    });
    
    // Newsletter form handling
    const newsletterForm = document.querySelector('.newsletter form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const email = this.querySelector('input[type="email"]').value;
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            
            // Basic email validation
            if (!email || !email.includes('@')) {
                alert('<?= $escaper->escapeJs(__('Please enter a valid email address.')) ?>');
                return;
            }
            
            // Update button state
            submitBtn.textContent = '<?= $escaper->escapeJs(__('Subscribing...')) ?>';
            submitBtn.disabled = true;
            
            // Simulate API call (replace with actual newsletter subscription logic)
            setTimeout(() => {
                submitBtn.textContent = '<?= $escaper->escapeJs(__('Subscribed!')) ?>';
                submitBtn.classList.add('bg-green-500', 'hover:bg-green-600');
                submitBtn.classList.remove('bg-diamond-blue', 'hover:bg-diamond-blue-dark');
                
                // Reset form
                this.querySelector('input[type="email"]').value = '';
                
                // Show success message
                const successMsg = document.createElement('div');
                successMsg.className = 'text-green-600 text-sm mt-2';
                successMsg.textContent = '<?= $escaper->escapeJs(__('Thank you for subscribing to our newsletter!')) ?>';
                this.appendChild(successMsg);
                
                // Reset button after 3 seconds
                setTimeout(() => {
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('bg-green-500', 'hover:bg-green-600');
                    submitBtn.classList.add('bg-diamond-blue', 'hover:bg-diamond-blue-dark');
                    if (successMsg.parentNode) {
                        successMsg.remove();
                    }
                }, 3000);
            }, 1500);
        });
    }
    
    // Product card hover effects
    document.querySelectorAll('.product-showcase .product-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('product-image-loading');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            img.classList.add('product-image-loading');
            imageObserver.observe(img);
        });
    }
    
    // Performance monitoring
    if ('performance' in window && 'measure' in window.performance) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData) {
                    console.log('Diamond Homepage Performance:', {
                        'DOM Content Loaded': perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart,
                        'Load Complete': perfData.loadEventEnd - perfData.loadEventStart,
                        'Total Load Time': perfData.loadEventEnd - perfData.navigationStart
                    });
                }
            }, 0);
        });
    }
    
    // Add scroll-based header transparency (if header exists)
    const header = document.querySelector('.header');
    if (header) {
        let lastScrollY = window.scrollY;
        
        window.addEventListener('scroll', () => {
            const scrollY = window.scrollY;
            
            if (scrollY > 100) {
                header.classList.add('header-scrolled');
            } else {
                header.classList.remove('header-scrolled');
            }
            
            // Hide/show header on scroll
            if (scrollY > lastScrollY && scrollY > 200) {
                header.style.transform = 'translateY(-100%)';
            } else {
                header.style.transform = 'translateY(0)';
            }
            
            lastScrollY = scrollY;
        });
    }
    
    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        .animate-in {
            animation: slideInUp 0.6s ease-out forwards;
        }
        
        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            transition: all 0.3s ease;
        }
        
        .header-scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }
        
        .product-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
    `;
    document.head.appendChild(style);
});

// Global functions for Alpine.js components
window.DiamondHomepage = {
    // Utility function for formatting currency
    formatPrice: function(price) {
        return new Intl.NumberFormat('de-AT', {
            style: 'currency',
            currency: 'EUR'
        }).format(price);
    },
    
    // Function to track events (replace with actual analytics)
    trackEvent: function(eventName, properties = {}) {
        console.log('Track Event:', eventName, properties);
        
        // Example: Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', eventName, properties);
        }
        
        // Example: Facebook Pixel
        if (typeof fbq !== 'undefined') {
            fbq('track', eventName, properties);
        }
    },
    
    // Function to handle product quick view
    quickView: function(productId) {
        this.trackEvent('quick_view', { product_id: productId });
        // Implement quick view modal logic here
        console.log('Quick view for product:', productId);
    },
    
    // Function to handle add to wishlist
    addToWishlist: function(productId) {
        this.trackEvent('add_to_wishlist', { product_id: productId });
        // Implement wishlist logic here
        console.log('Add to wishlist:', productId);
    }
};
</script>
