<?php
/**
 * Diamond Aircraft Homepage Hero Section
 * Replicates the hero slider from the Live Site
 */

declare(strict_types=1);

use <PERSON>yva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

// Hero slider configuration
$heroSlides = [
    [
        'image' => 'header_home_da50rg_5_2.jpg',
        'title' => '#WeWearDiamondAircraft',
        'subtitle' => 'Discover our premium collection of Diamond Aircraft apparel and accessories',
        'cta_text' => 'Shop Now',
        'cta_link' => '/apparel.html',
        'overlay_position' => 'left'
    ]
];
?>

<script>
function heroSlider() {
    return {
        currentSlide: 0,
        slides: <?= json_encode($heroSlides) ?>,
        autoplay: true,
        autoplayInterval: 5000,

        init() {
            if (this.autoplay && this.slides.length > 1) {
                setInterval(() => {
                    this.nextSlide();
                }, this.autoplayInterval);
            }
        },

        nextSlide() {
            this.currentSlide = (this.currentSlide + 1) % this.slides.length;
        },

        prevSlide() {
            this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
        },

        goToSlide(index) {
            this.currentSlide = index;
        }
    }
}
</script>

<!-- Diamond Aircraft Hero Slider -->
<div class="hero-slider relative w-full h-screen overflow-hidden" x-data="heroSlider()" x-init="init()">

    <!-- Slides Container -->
    <div class="relative w-full h-full">
        <?php foreach ($heroSlides as $index => $slide): ?>
        <div
            class="absolute inset-0 w-full h-full transition-opacity duration-1000 ease-in-out"
            x-show="currentSlide === <?= $index ?>"
            x-transition:enter="transition-opacity duration-1000"
            x-transition:enter-start="opacity-0"
            x-transition:enter-end="opacity-100"
            x-transition:leave="transition-opacity duration-1000"
            x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
        >
            <!-- Background Image -->
            <div class="absolute inset-0 bg-cover bg-center bg-no-repeat"
                 style="background-image: url('<?= $escaper->escapeUrl($block->getViewFileUrl('images/hero/' . $slide['image'])) ?>')">
                <!-- Dark Overlay -->
                <div class="absolute inset-0 bg-black bg-opacity-40"></div>
            </div>

            <!-- Content Overlay -->
            <div class="relative z-10 flex items-center h-full">
                <div class="max-w-screen-2xl mx-auto px-4 w-full">
                    <div class="<?= $slide['overlay_position'] === 'center' ? 'text-center' : 'text-left' ?> max-w-2xl <?= $slide['overlay_position'] === 'right' ? 'ml-auto' : '' ?>">

                        <!-- Main Title -->
                        <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
                            <?= $escaper->escapeHtml($slide['title']) ?>
                        </h1>

                        <!-- Subtitle -->
                        <?php if (!empty($slide['subtitle'])): ?>
                        <p class="text-lg md:text-xl text-white mb-8 leading-relaxed opacity-90">
                            <?= $escaper->escapeHtml($slide['subtitle']) ?>
                        </p>
                        <?php endif; ?>

                        <!-- Call to Action Button -->
                        <div class="flex <?= $slide['overlay_position'] === 'center' ? 'justify-center' : 'justify-start' ?>">
                            <a href="<?= $escaper->escapeUrl($slide['cta_link']) ?>"
                               class="inline-flex items-center px-8 py-4 bg-diamond-blue hover:bg-diamond-blue-dark text-white font-bold text-lg rounded-lg transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                                <?= $escaper->escapeHtml($slide['cta_text']) ?>
                                <span class="ml-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                                    </svg>
                                </span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>

    <!-- Navigation Dots (if multiple slides) -->
    <?php if (count($heroSlides) > 1): ?>
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20">
        <div class="flex space-x-3">
            <?php foreach ($heroSlides as $index => $slide): ?>
            <button
                @click="goToSlide(<?= $index ?>)"
                class="w-3 h-3 rounded-full transition-all duration-300"
                :class="currentSlide === <?= $index ?> ? 'bg-white' : 'bg-white bg-opacity-50 hover:bg-opacity-75'"
                aria-label="<?= $escaper->escapeHtmlAttr(__('Go to slide %1', $index + 1)) ?>"
            ></button>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Navigation Arrows (if multiple slides) -->
    <?php if (count($heroSlides) > 1): ?>
    <button
        @click="prevSlide()"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-black bg-opacity-30 hover:bg-opacity-50 text-white rounded-full transition-all duration-300 hover:scale-110"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Previous slide')) ?>"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
        </svg>
    </button>

    <button
        @click="nextSlide()"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 z-20 p-3 bg-black bg-opacity-30 hover:bg-opacity-50 text-white rounded-full transition-all duration-300 hover:scale-110"
        aria-label="<?= $escaper->escapeHtmlAttr(__('Next slide')) ?>"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
    </button>
    <?php endif; ?>

    <!-- Scroll Down Indicator -->
    <div class="absolute bottom-8 right-8 z-20 animate-bounce">
        <div class="flex flex-col items-center text-white opacity-75">
            <span class="text-sm mb-2 font-medium">Scroll</span>
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
            </svg>
        </div>
    </div>
</div>
