<?php
/**
 * Diamond Aircraft Product List Item Template
 * Based on Hyvä Themes - https://hyva.io
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\BlockJsDependencies;
use Hyva\Theme\ViewModel\ProductCompare;
use Hyva\Theme\ViewModel\ProductListItem;
use Hyva\Theme\ViewModel\ProductPage;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Block\Product\AbstractProduct;
use Magento\Catalog\Helper\Output as CatalogOutputHelper;
use Magento\Catalog\ViewModel\Product\OptionsData as ProductOptionsData;
use Magento\Framework\Escaper;

/** @var AbstractProduct $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);

/** @var ProductListItem $productListItemViewModel */
$productListItemViewModel = $viewModels->require(ProductListItem::class);

/** @var CatalogOutputHelper $catalogOutputHelper */
$catalogOutputHelper = $this->helper(CatalogOutputHelper::class);

/** @var ProductCompare $compareViewModel */
$compareViewModel = $viewModels->require(ProductCompare::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

/** @var HeroiconsSolid $heroiconsSolid */
$heroiconsSolid = $viewModels->require(HeroiconsSolid::class);

$productOptionsViewmodel = $viewModels->require(ProductOptionsData::class);

/** @var Magento\Catalog\Model\Product $product */
if (! ($product = $block->getData('product'))) {
    return;
}
$imageDisplayArea = $block->getData('image_display_area');
$templateType = $block->getData('template_type');
$viewMode = $block->getData('view_mode');
$showDescription = $block->getData('show_description');
$showAddToWishlist = $wishlistViewModel->isEnabled();
$showAddToCompare = $compareViewModel->showInProductList();
$viewIsGrid = $viewMode === 'grid';
$productType = $product->getTypeId();
$isProductGroupedOrBundle = $productType === 'bundle' || $productType === "grouped";
$productId = $product->getId();
$options   = $productOptionsViewmodel->getOptionsData($product);

$hideDetails       = $block->getData('hide_details') ?: false;
$hideRatingSummary = $block->getData('hide_rating_summary') ?: false;

$imageCustomAttributes = $product->getData('image_custom_attributes')
    ?? $block->getData('image_custom_attributes')
    ?? [];
$productName = $catalogOutputHelper->productAttribute($product, $product->getName(), 'name');

// Ensure the required JS is rendered on the page
$viewModels->require(BlockJsDependencies::class)->setBlockTemplateDependency($block, 'Magento_Catalog::product/list/js/price-box.phtml');

?>

<?php if ($product->isSaleable()): ?>
<form method="post"
    action="<?= $escaper->escapeUrl($productViewModel->getAddToCartUrl($product, ['useUencPlaceholder' => true])) ?>"
    class="group bg-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-gray-200 hover:border-diamond-blue flex flex-col h-full <?= $viewIsGrid ? '' : 'md:flex-row' ?>"
    <?php if ($product->getOptions()): ?>
    enctype="multipart/form-data"
    <?php endif; ?>
    <?php foreach ($block->getData('product_data_attributes') ?? [] as $attribute => $include): ?>
        <?php if ($include): ?>
            <?php /** Append data-* attributes based on layout XML arguments */ ?>
            <?= $escaper->escapeHtml(sprintf('data-%s', $attribute)); ?>="<?= $escaper->escapeHtmlAttr($product->getData($attribute)); ?>"
        <?php endif; ?>
    <?php endforeach; ?>
>
    <?= /** @noEscape */ $block->getBlockHtml('formkey') ?>
    <input type="hidden" name="product" value="<?= (int)$productId ?>"/>

    <!-- Product Image -->
    <div class="relative overflow-hidden <?= $viewIsGrid ? 'aspect-square' : 'md:w-1/3' ?>">
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
           class="block w-full h-full"
           title="<?= $escaper->escapeHtmlAttr($productName) ?>">
            <?= $block->getImage($product, $imageDisplayArea, $imageCustomAttributes)->toHtml() ?>
        </a>

        <!-- Sale Badge -->
        <?php if ($product->getSpecialPrice()): ?>
            <div class="absolute top-3 left-3 bg-secondary text-white text-xs font-bold px-2 py-1 rounded-md">
                <?= $escaper->escapeHtml(__('SALE')) ?>
            </div>
        <?php endif; ?>

        <!-- Wishlist & Compare Actions -->
        <div class="absolute top-3 right-3 flex flex-col space-y-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
            <?php if ($showAddToWishlist): ?>
                <button type="button"
                        class="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-sm hover:shadow-md transition-all duration-200"
                        title="<?= $escaper->escapeHtmlAttr(__('Add to Wishlist')) ?>"
                        x-data="initWishlist()"
                        @click.prevent="addToWishlist(<?= (int)$productId ?>)">
                    <?= $heroicons->heartHtml('w-4 h-4 text-gray-600 hover:text-secondary', 16, 16) ?>
                </button>
            <?php endif; ?>

            <?php if ($showAddToCompare): ?>
                <button type="button"
                        class="bg-white bg-opacity-90 hover:bg-opacity-100 rounded-full p-2 shadow-sm hover:shadow-md transition-all duration-200"
                        title="<?= $escaper->escapeHtmlAttr(__('Add to Compare')) ?>"
                        x-data="initCompareOnProductList()"
                        @click.prevent="addToCompare(<?= (int)$productId ?>)">
                    <?= $heroicons->scaleHtml('w-4 h-4 text-gray-600 hover:text-diamond-blue', 16, 16) ?>
                </button>
            <?php endif; ?>
        </div>
    </div>

    <!-- Product Details -->
    <div class="flex flex-col flex-1 p-4 <?= $viewIsGrid ? '' : 'md:w-2/3' ?>">
        <!-- Product Name -->
        <h3 class="text-lg font-semibold text-gray-800 mb-2 line-clamp-2 hover:text-diamond-blue transition-colors duration-200">
            <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
               title="<?= $escaper->escapeHtmlAttr($productName) ?>">
                <?= $escaper->escapeHtml($productName) ?>
            </a>
        </h3>

        <!-- Rating Summary -->
        <?php if (!$hideRatingSummary): ?>
            <div class="mb-3">
                <?= $block->getReviewsSummaryHtml($product, $templateType) ?>
            </div>
        <?php endif; ?>

        <!-- Product Description -->
        <?php if ($showDescription && !$hideDetails): ?>
            <div class="text-sm text-gray-600 mb-3 line-clamp-3">
                <?= $escaper->escapeHtml($product->getShortDescription()) ?>
            </div>
        <?php endif; ?>

        <!-- Price -->
        <div class="mb-4 mt-auto">
            <?= /** @noEscape */ $productListItemViewModel->getProductPriceHtml($product) ?>
        </div>

        <!-- Stock Status -->
        <div class="mb-4">
            <?php if ($product->isAvailable()): ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3"/>
                    </svg>
                    <?= $escaper->escapeHtml(__('In Stock')) ?>
                </span>
            <?php else: ?>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                    <svg class="w-2 h-2 mr-1" fill="currentColor" viewBox="0 0 8 8">
                        <circle cx="4" cy="4" r="3"/>
                    </svg>
                    <?= $escaper->escapeHtml(__('Out of Stock')) ?>
                </span>
            <?php endif; ?>
        </div>

        <!-- Add to Cart Button -->
        <?php if ($product->isSaleable() && !$isProductGroupedOrBundle): ?>
            <button type="submit"
                    class="w-full bg-diamond-blue hover:bg-diamond-blue-dark text-white font-semibold py-3 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-diamond-blue focus:ring-opacity-50"
                    title="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>">
                <span class="flex items-center justify-center">
                    <?= $heroicons->shoppingCartHtml('w-5 h-5 mr-2', 20, 20) ?>
                    <?= $escaper->escapeHtml(__('Add to Cart')) ?>
                </span>
            </button>
        <?php else: ?>
            <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
               class="w-full bg-gray-600 hover:bg-gray-700 text-white font-semibold py-3 px-4 rounded-md transition-colors duration-200 text-center inline-block">
                <?= $escaper->escapeHtml(__('View Details')) ?>
            </a>
        <?php endif; ?>
    </div>
</form>
<?php else: ?>
<!-- Out of Stock Product -->
<div class="group bg-white rounded-lg shadow-sm border border-gray-200 flex flex-col h-full opacity-75 <?= $viewIsGrid ? '' : 'md:flex-row' ?>">
    <!-- Product Image -->
    <div class="relative overflow-hidden <?= $viewIsGrid ? 'aspect-square' : 'md:w-1/3' ?>">
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
           class="block w-full h-full"
           title="<?= $escaper->escapeHtmlAttr($productName) ?>">
            <?= $block->getImage($product, $imageDisplayArea, $imageCustomAttributes)->toHtml() ?>
        </a>

        <!-- Out of Stock Overlay -->
        <div class="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
            <span class="bg-red-500 text-white text-sm font-bold px-3 py-1 rounded-md">
                <?= $escaper->escapeHtml(__('Out of Stock')) ?>
            </span>
        </div>
    </div>

    <!-- Product Details -->
    <div class="flex flex-col flex-1 p-4 <?= $viewIsGrid ? '' : 'md:w-2/3' ?>">
        <!-- Product Name -->
        <h3 class="text-lg font-semibold text-gray-600 mb-2 line-clamp-2">
            <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
               title="<?= $escaper->escapeHtmlAttr($productName) ?>">
                <?= $escaper->escapeHtml($productName) ?>
            </a>
        </h3>

        <!-- Price -->
        <div class="mb-4 mt-auto">
            <?= /** @noEscape */ $productListItemViewModel->getProductPriceHtml($product) ?>
        </div>

        <!-- View Details Button -->
        <a href="<?= $escaper->escapeUrl($product->getProductUrl()) ?>"
           class="w-full bg-gray-400 text-white font-semibold py-3 px-4 rounded-md text-center inline-block cursor-not-allowed">
            <?= $escaper->escapeHtml(__('View Details')) ?>
        </a>
    </div>
</div>
<?php endif; ?>

<!-- Required JS Templates -->
<?= $block->getChildHtml('js') ?>
